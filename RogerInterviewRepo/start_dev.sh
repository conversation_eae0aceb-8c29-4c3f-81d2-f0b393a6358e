#!/bin/bash

# Healthcare Data Platform - Development Startup Script

echo "🏥 Starting Healthcare Data Platform"
echo "================================================"

# Function to check Redis connectivity with multiple methods
check_redis() {
    echo "🔍 Checking Redis connectivity..."
    
    # Method 1: Try redis-cli if available
    if command -v redis-cli >/dev/null 2>&1; then
        echo "   Trying redis-cli ping..."
        if redis-cli -h localhost -p 6379 ping > /dev/null 2>&1; then
            echo "✅ Redis is running (verified with redis-cli)"
            return 0
        fi
    else
        echo "   redis-cli not found in PATH (Docker installation?)"
    fi
    
    # Method 2: Check if port 6379 is open using netcat
    if command -v nc >/dev/null 2>&1; then
        echo "   Trying netcat connection test..."
        if echo "PING" | nc -w 1 localhost 6379 | grep -q "PONG" 2>/dev/null; then
            echo "✅ Redis is running (verified with netcat)"
            return 0
        fi
    fi
    
    # Method 3: Check if port 6379 is listening using telnet
    if command -v telnet >/dev/null 2>&1; then
        echo "   Trying telnet connection test..."
        if timeout 2 bash -c "</dev/tcp/localhost/6379" 2>/dev/null; then
            echo "✅ Redis port 6379 is open (connection test passed)"
            return 0
        fi
    fi
    
    # Method 4: Python-based check as last resort
    if command -v python3 >/dev/null 2>&1; then
        echo "   Trying Python socket connection..."
        if python3 -c "
import socket
import sys
try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(2)
    result = sock.connect_ex(('localhost', 6379))
    sock.close()
    if result == 0:
        print('✅ Redis port 6379 is accessible')
        sys.exit(0)
    else:
        sys.exit(1)
except:
    sys.exit(1)
" 2>/dev/null; then
            return 0
        fi
    fi
    
    # All methods failed
    echo "❌ Redis is not running or not accessible on localhost:6379"
    echo ""
    echo "Please start Redis using one of these methods:"
    echo "   Native install:"
    echo "     macOS: brew services start redis"
    echo "     Linux: sudo systemctl start redis"
    echo "   Docker:"
    echo "     docker run -d --name redis -p 6379:6379 redis:alpine"
    echo "   Docker Compose:"
    echo "     Add to docker-compose.yml and run: docker-compose up -d redis"
    echo ""
    echo "To verify Redis is running:"
    echo "   With redis-cli: redis-cli ping"
    echo "   With Docker: docker exec redis redis-cli ping"
    echo "   Check port: nc -zv localhost 6379"
    return 1
}

# Check Redis connectivity
if ! check_redis; then
    exit 1
fi

# Function to check if port is available
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "❌ Port $1 is already in use"
        return 1
    else
        echo "✅ Port $1 is available"
        return 0
    fi
}

# Check ports
check_port 8000 || exit 1
check_port 3000 || exit 1

# Setup backend
echo ""
echo "🐍 Setting up Python backend..."
cd backend

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Start FastAPI server in background
echo "Starting FastAPI server..."
uvicorn main:app --reload --port 8000 &
BACKEND_PID=$!

# Start arq worker with hot reload in background
echo "Starting background worker with hot reload..."
watchmedo auto-restart --recursive -- arq worker.WorkerSettings &
WORKER_PID=$!

cd ..

# Setup frontend
echo ""
echo "⚛️ Setting up React frontend..."
cd frontend

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "Installing Node.js dependencies..."
    npm install
fi

# Start React development server in background
echo "Starting React development server..."
npm start &
FRONTEND_PID=$!

cd ..

echo ""
echo "🚀 All services started successfully!"
echo "================================================"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"
echo "💾 Form data will be saved to: backend/health_form_data.json"
echo ""
echo "Press Ctrl+C to stop all services"

# Function to cleanup processes
cleanup() {
    echo ""
    echo "🛑 Stopping all services..."
    kill $BACKEND_PID $WORKER_PID $FRONTEND_PID 2>/dev/null
    echo "👋 Goodbye!"
    exit 0
}

# Set trap to cleanup on exit
trap cleanup SIGINT SIGTERM

# Wait for all background processes
wait 