{"name": "affordance-discovery-mcp", "version": "0.1.0", "description": "An MCP server that enables AI agents to iteratively discover available affordances (packages, modules, functions) to prevent import hallucination", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "prepare": "npm run build"}, "keywords": ["mcp", "model-context-protocol", "ai", "agents", "affordances", "package-discovery", "import-validation", "code-generation"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/affordance-discovery-mcp.git"}, "bugs": {"url": "https://github.com/yourusername/affordance-discovery-mcp/issues"}, "homepage": "https://github.com/yourusername/affordance-discovery-mcp#readme", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "tsx": "^4.6.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}, "engines": {"node": ">=18.0.0"}, "files": ["dist", "README.md", "LICENSE"]}