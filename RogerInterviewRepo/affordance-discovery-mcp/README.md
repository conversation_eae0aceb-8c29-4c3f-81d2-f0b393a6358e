# Affordance Discovery MCP Server

An MCP (Model Context Protocol) server that enables AI agents to iteratively discover available affordances (packages, modules, functions) to prevent import hallucination in code generation.

## Problem Statement

AI-powered code generation tools frequently "hallucinate" imports - they generate code that references packages, modules, or functions that don't exist in the target environment. This leads to:

- ❌ Code that fails to run
- ❌ Wasted development time
- ❌ Reduced trust in AI coding tools
- ❌ Supply chain security concerns

## Solution

This MCP server provides AI agents with the ability to:

- ✅ **Discover available packages** in the current environment
- ✅ **Validate imports** before generating code
- ✅ **Introspect module contents** to understand available functions/classes
- ✅ **Cache results** for performance while maintaining accuracy
- ✅ **Support multiple languages** (JavaScript, Python, Rust, Go, etc.)

## How It Works

1. **AI Agent Query**: Agent asks "What packages are available for React development?"
2. **Discovery**: Server scans npm packages, validates installations
3. **Introspection**: Server analyzes available exports from discovered packages
4. **Response**: Agent receives accurate list of available affordances
5. **Code Generation**: Agent generates code using only verified imports

## Trade-off: Latency vs. Accuracy

This approach trades some latency for significantly improved accuracy:
- **Cost**: Slight delay for discovery queries
- **Benefit**: Eliminates hallucinated imports and broken code

## Quick Start

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Build for production
npm run build
npm start
```

## Usage with AI Agents

Configure your AI agent to use this MCP server:

```json
{
  "mcpServers": {
    "affordance-discovery": {
      "command": "node",
      "args": ["path/to/affordance-discovery-mcp/dist/index.js"],
      "env": {}
    }
  }
}
```

## Available Tools

- `discover_packages`: Find available packages in the environment
- `validate_import`: Check if a specific import is valid
- `introspect_module`: Get detailed information about a module's exports
- `search_affordances`: Search for packages/modules by functionality

## Supported Languages

- [x] JavaScript/Node.js (npm)
- [ ] Python (pip, conda)
- [ ] Rust (cargo)
- [ ] Go (go modules)
- [ ] Java (maven, gradle)

## Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

## License

MIT License - see [LICENSE](LICENSE) for details.

## Roadmap

- [ ] Multi-language support
- [ ] Advanced caching strategies
- [ ] Integration with popular AI coding tools
- [ ] Performance benchmarks
- [ ] Plugin system for custom discovery engines
