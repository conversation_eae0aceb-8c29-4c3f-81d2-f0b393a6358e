# Backend Setup Guide

## Prerequisites

- Python 3.8+
- Redis server (for job queue)

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Install and start Redis:**
   ```bash
   # macOS with Homebrew
   brew install redis
   brew services start redis
   
   # Ubuntu/Debian
   sudo apt install redis-server
   sudo systemctl start redis
   
   # Or use Docker
   docker run -d -p 6379:6379 redis:alpine
   ```

## Running the Application

1. **Start the FastAPI server (with hot reload):**
   ```bash
   uvicorn main:app --reload --port 8000
   ```

2. **In a separate terminal, start the worker (with hot reload):**
   ```bash
   watchmedo auto-restart --recursive -- arq worker.WorkerSettings
   ```

## API Endpoints

- `GET /` - Root endpoint
- `GET /health` - Health check
- `GET /form` - Get current health form data
- `PUT /form` - Update health form data
- `POST /form/improve-grammar` - Trigger LLM grammar improvement (async)
- `GET /jobs/{job_id}/status` - Check job status

## API Documentation

Once the server is running, visit:
- Interactive docs: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Architecture

- **FastAPI**: Web framework with automatic OpenAPI documentation
- **JSON File Storage**: Simple persistence using `health_form_data.json`
- **arq**: Redis-based job queue for background processing
- **OpenAI + OpenRouter**: LLM integration for health data grammar improvement using Llama 4 Maverick
- **Redis**: In-memory store for job queue management

## Data Flow

1. Health form data is stored in `health_form_data.json`
2. When grammar improvement is triggered, a background job is queued
3. The worker processes the job using OpenAI + OpenRouter (Llama 4 Maverick)
4. The LLM analyzes health responses for grammatical issues
5. Improved data is saved back to the JSON file
6. Frontend can poll job status for real-time updates

