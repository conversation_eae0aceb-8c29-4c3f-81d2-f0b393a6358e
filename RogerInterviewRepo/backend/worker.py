import asyncio
import os
from typing import Dict, Any
from arq import create_pool
from arq.connections import RedisSettings
from openai import AsyncOpenAI
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# File to store form data (same as in main.py)
FORM_DATA_FILE = "health_form_data.json"

def load_form_data() -> dict:
    """Load form data from file, return empty dict if file doesn't exist."""
    if os.path.exists(FORM_DATA_FILE):
        try:
            with open(FORM_DATA_FILE, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            pass
    return {}

def save_form_data(data: dict):
    """Save form data to file."""
    with open(FORM_DATA_FILE, 'w') as f:
        json.dump(data, f, indent=2, default=str)

async def process_health_data_grammar(ctx, form_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process health form data for grammar improvement using LLM.
    This is the main background job that will be executed by arq.
    """
    try:
        logger.info("Starting grammar check for health form")
        
        # Prepare the current answers for LLM processing
        current_answers = {
            "chest_pain_frequency": form_data.get("chest_pain_frequency"),
            "exercise_tolerance": form_data.get("exercise_tolerance"),
            "family_heart_history": form_data.get("family_heart_history"),
            "current_medications": form_data.get("current_medications"),
            "smoking_status": form_data.get("smoking_status")
        }
        
        # Create prompt for LLM
        prompt = f"""
You are an expert medical documentation quality reviewer. Review the following health form responses and improve their grammar to ensure they are written in complete, grammatically valid sentences. 

CURRENT HEALTH FORM RESPONSES:
• Chest Pain Frequency: "{current_answers['chest_pain_frequency']}"
• Exercise Tolerance: "{current_answers['exercise_tolerance']}"
• Family Heart Disease History: "{current_answers['family_heart_history']}"
• Current Medications: "{current_answers['current_medications']}"
• Smoking Status: "{current_answers['smoking_status']}"

INSTRUCTIONS:
1. For each field, output one of the following:
   - The original answer exactly as-is (if it's already grammatically correct)
   - A new answer with minimal changes to make it a grammatically correct sentence (if it's not already grammatically correct)

2. Only make changes if the grammar actually needs improvement
3. Preserve the original meaning and medical information completely
4. Convert fragments, bullet points, or incomplete sentences into proper complete sentences
5. Fix spelling, punctuation, and grammatical errors
6. Ensure responses sound natural and professional

IMPORTANT: Only change answers that actually have grammatical issues. If an answer is already grammatically correct, keep it exactly the same.
"""
        
        # Define the JSON schema for OpenAI structured output
        json_schema = {
            "name": "improve_health_form_grammar",
            "strict": True,
            "schema": {
                "type": "object",
                "properties": {
                    "chest_pain_frequency": {
                        "type": "string",
                        "description": "Chest pain frequency response - keep original if already grammatically correct, otherwise improve grammar"
                    },
                    "exercise_tolerance": {
                        "type": "string", 
                        "description": "Exercise tolerance response - keep original if already grammatically correct, otherwise improve grammar"
                    },
                    "family_heart_history": {
                        "type": "string",
                        "description": "Family heart history response - keep original if already grammatically correct, otherwise improve grammar"
                    },
                    "current_medications": {
                        "type": "string",
                        "description": "Current medications response - keep original if already grammatically correct, otherwise improve grammar"
                    },
                    "smoking_status": {
                        "type": "string",
                        "description": "Smoking status response - keep original if already grammatically correct, otherwise improve grammar"
                    }
                },
                "required": [
                    "chest_pain_frequency",
                    "exercise_tolerance",
                    "family_heart_history",
                    "current_medications",
                    "smoking_status"
                ],
                "additionalProperties": False
            }
        }

        # Call OpenAI API with OpenRouter
        openai_client = AsyncOpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key="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"
        )

        response = await openai_client.chat.completions.create(
            # Can choose from any of these 3 free models
            model="meta-llama/llama-4-scout:free",
            # model="mistralai/mistral-small-3.2-24b-instruct:free",
            # model="meta-llama/llama-4-maverick:free",
            messages=[
                {"role": "user", "content": prompt}
            ],
            response_format={
                "type": "json_schema",
                "json_schema": json_schema
            },
            temperature=0.0
        )

        # Extract the JSON response
        response_content = response.choices[0].message.content
        if not response_content:
            raise ValueError("LLM did not return any response content")
        
        improved_data = json.loads(response_content)

        # Log the prompt and response for debugging
        logger.info(f"Prompt sent: {prompt}")
        logger.info(f"Improved data received: {improved_data}")
        
        # Load current form data and update it with improved data
        current_form_data = load_form_data()
        current_form_data.update({
            "chest_pain_frequency": improved_data.get("chest_pain_frequency"),
            "exercise_tolerance": improved_data.get("exercise_tolerance"),
            "family_heart_history": improved_data.get("family_heart_history"),
            "current_medications": improved_data.get("current_medications"),
            "smoking_status": improved_data.get("smoking_status"),
            "is_processing": False,
            "job_id": None,
            "updated_at": form_data.get("updated_at")  # Keep the original update time
        })
        
        # Save updated form data
        save_form_data(current_form_data)
        
        logger.info("Successfully completed grammar check for health form")
        
        return {
            "status": "completed",
            "improved_data": improved_data,
            "original_data": current_answers
        }
        
    except Exception as e:
        logger.error(f"Error processing health form: {str(e)}")
        
        # Mark as not processing in case of error
        current_form_data = load_form_data()
        current_form_data.update({
            "is_processing": False,
            "job_id": None
        })
        save_form_data(current_form_data)
        
        raise

# Redis settings for arq
class WorkerSettings:
    redis_settings = RedisSettings()
    functions = [process_health_data_grammar]
    keep_result = 3600  # Keep job results for 1 hour 