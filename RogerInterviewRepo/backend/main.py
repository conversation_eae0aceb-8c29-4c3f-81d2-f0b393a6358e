from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager
import uuid
import json
import os
from datetime import datetime
from arq import create_pool
from arq.connections import RedisSettings
from arq.jobs import Job

# Redis pool for job queue
redis_pool = None

# File to store form data
FORM_DATA_FILE = "health_form_data.json"

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global redis_pool
    redis_settings = RedisSettings(
        host="localhost",
        port=6379,
        database=0
    )
    redis_pool = await create_pool(redis_settings)
    yield
    # Shutdown
    if redis_pool:
        redis_pool.close()
        await redis_pool.wait_closed()

app = FastAPI(
    title="Healthcare Form Platform",
    description="Simple cardiovascular health form with LLM grammar improvement",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def load_form_data() -> dict:
    """Load form data from file, return empty dict if file doesn't exist."""
    if os.path.exists(FORM_DATA_FILE):
        try:
            with open(FORM_DATA_FILE, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            pass
    return {}

def save_form_data(data: dict):
    """Save form data to file."""
    with open(FORM_DATA_FILE, 'w') as f:
        json.dump(data, f, indent=2, default=str)

@app.get("/")
async def root():
    return {"message": "Healthcare Form Platform API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/form")
async def get_form():
    """Get the current health form data."""
    data = load_form_data()
    
    # Return empty form if no data exists
    return {
        "chest_pain_frequency": data.get("chest_pain_frequency"),
        "exercise_tolerance": data.get("exercise_tolerance"),
        "family_heart_history": data.get("family_heart_history"),
        "current_medications": data.get("current_medications"),
        "smoking_status": data.get("smoking_status"),
        "updated_at": data.get("updated_at"),
        "is_processing": data.get("is_processing", False),
        "job_id": data.get("job_id")
    }

@app.put("/form")
async def update_form(form_data: Dict[str, Any]):
    """Update the health form data and save to file."""
    
    # Load existing data
    existing_data = load_form_data()
    
    # Update with new form data (filter out None values)
    filtered_form_data = {k: v for k, v in form_data.items() if v is not None}
    updated_data = {
        **existing_data,
        **filtered_form_data,
        "updated_at": datetime.now().isoformat()
    }
    
    # Save to file
    save_form_data(updated_data)
    
    return {
        "chest_pain_frequency": updated_data.get("chest_pain_frequency"),
        "exercise_tolerance": updated_data.get("exercise_tolerance"),
        "family_heart_history": updated_data.get("family_heart_history"),
        "current_medications": updated_data.get("current_medications"),
        "smoking_status": updated_data.get("smoking_status"),
        "updated_at": updated_data["updated_at"],
        "is_processing": updated_data.get("is_processing", False),
        "job_id": updated_data.get("job_id")
    }

@app.post("/form/improve-grammar")
async def trigger_grammar_improvement(form_data: Optional[Dict[str, Any]] = None):
    """Trigger LLM-based grammar improvement for the health form."""
    global redis_pool
    
    if not redis_pool:
        raise HTTPException(status_code=500, detail="Job queue not available")
    
    # If form data is provided from UI, save it first
    if form_data:
        # Load existing data and merge with new form data
        existing_data = load_form_data()
        filtered_form_data = {k: v for k, v in form_data.items() if v is not None}
        data = {
            **existing_data,
            **filtered_form_data,
            "updated_at": datetime.now().isoformat()
        }
        # Save the updated form data from UI
        save_form_data(data)
    else:
        # Load current form data if none provided
        data = load_form_data()
    
    if not any([
        data.get("chest_pain_frequency"),
        data.get("exercise_tolerance"),
        data.get("family_heart_history"),
        data.get("current_medications"),
        data.get("smoking_status")
    ]):
        raise HTTPException(status_code=404, detail="No health form data found")
    
    if data.get("is_processing"):
        raise HTTPException(
            status_code=409, 
            detail="Form is already being processed"
        )
    
    # Generate unique job ID
    job_id = str(uuid.uuid4())
    
    # Mark form as processing and save again
    data["is_processing"] = True
    data["job_id"] = job_id
    save_form_data(data)
    
    # Queue the job
    job = await redis_pool.enqueue_job(
        'process_health_data_grammar',
        data,  # pass the form data
        _job_id=job_id
    )
    
    return {
        "message": "Grammar improvement initiated",
        "job_id": job_id
    }

@app.get("/jobs/{job_id}/status")
async def get_job_status(job_id: str):
    """Get the status of a background job."""
    global redis_pool
    
    if not redis_pool:
        raise HTTPException(status_code=500, detail="Job queue not available")
    
    try:
        job = Job(job_id, redis_pool)
        
        # Get job status
        job_status = await job.status()
        
        status = "pending"
        result = None
        error = None
        
        if job_status.value == "complete":
            status = "completed"
            try:
                result_info = await job.result_info()
                if result_info:
                    if result_info.success:
                        result = result_info.result
                    else:
                        status = "failed"
                        error = str(result_info.result) if result_info.result else "Job failed"
            except Exception:
                # If we can't get result info, job is still considered completed
                pass
        elif job_status.value == "in_progress":
            status = "in_progress"
        elif job_status.value == "not_found":
            raise HTTPException(status_code=404, detail="Job not found")
        elif job_status.value in ["queued", "deferred"]:
            status = "pending"
        
        return {
            "job_id": job_id,
            "status": status,
            "result": result,
            "error": error
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error checking job status: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 