# Healthcare Form Platform

A simple fullstack application for cardiovascular health assessment with automated grammar improvement.

## Project Structure

```
├── backend/          # Python FastAPI backend with async job processing
├── frontend/         # React frontend application
└── README.md        # This file
```

## Quick Start

### Prerequisites

- Python 3.8+
- Node.js 16+
- Redis server

### One-Command Setup & Run

1. **Start Redis** (if not already running):
   ```bash
   # macOS: brew services start redis
   # Linux: sudo systemctl start redis  
   # Docker: docker run -d -p 6379:6379 redis:alpine
   ```

2. **Start everything with hot reload**:
   ```bash
   ./start_dev.sh
   ```

3. **Access the Application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

4. **Stop everything**:
   ```bash
   # Press Ctrl+C in the terminal running start_dev.sh
   # The script will automatically stop all services
   ```

### Hot Reload Features ✨

- **FastAPI Backend**: Auto-reloads on Python file changes
- **React Frontend**: Auto-reloads on JavaScript/CSS changes  
- **arq Worker**: Auto-restarts on Python file changes (using watchmedo)
- **Data Storage**: JSON file automatically created on first run

### Manual Setup (Alternative)

If you prefer to run services individually in separate terminals:

```bash
# Terminal 1 - Backend
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --port 8000

# Terminal 2 - Worker (with hot reload)
cd backend
watchmedo auto-restart --recursive -- arq worker.WorkerSettings

# Terminal 3 - Frontend  
cd frontend
npm install
npm start
```

## Technology Stack

### Backend
- **FastAPI**: Modern Python web framework
- **JSON File Storage**: Simple local file persistence (no database setup required)
- **arq**: Redis-based job queue for async processing
- **Redis**: In-memory data store for job queue
- **OpenAI + OpenRouter**: LLM integration for grammar improvement using Llama 4 Maverick

### Frontend
- **React**: JavaScript library for building user interfaces
- **Axios**: HTTP client for API communication
- **CSS**: Modern styling with responsive design



## How It Works

### Data Flow
1. **Frontend Form**: User fills out 5 cardiovascular health questions
2. **JSON Storage**: Form data is saved to `backend/health_form_data.json`
3. **Async Processing**: When user triggers grammar improvement, a background job is queued
4. **LLM Analysis**: OpenAI + OpenRouter (Llama 4 Maverick) analyzes the health responses for grammatical issues
5. **Smart Improvements**: The LLM makes minimal changes to improve grammar while preserving meaning
6. **Real-time Updates**: Frontend polls job status and displays results

### API Endpoints
- `GET /form` - Retrieve current health form data
- `PUT /form` - Update health form data
- `POST /form/improve-grammar` - Trigger LLM grammar improvement (async)
- `GET /jobs/{job_id}/status` - Check background job status
