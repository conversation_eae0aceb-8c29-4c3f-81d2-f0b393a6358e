import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Health Form API endpoints
export const formAPI = {
  // Get the health form
  get: () => api.get('/form'),
  
  // Update the health form
  update: (formData) => api.put('/form', formData),
  
  // Trigger grammar improvement
  improveGrammar: () => api.post('/form/improve-grammar'),
};

// Job API endpoints
export const jobAPI = {
  // Get job status
  getStatus: (jobId) => api.get(`/jobs/${jobId}/status`),
};

// Health check
export const healthCheck = () => api.get('/health');

export default api; 