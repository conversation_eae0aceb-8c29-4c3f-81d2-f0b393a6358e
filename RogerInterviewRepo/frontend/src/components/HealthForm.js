import React, { useState, useEffect } from 'react';
import { formAPI, jobAPI } from '../services/api';

const HealthForm = () => {
  const [formData, setFormData] = useState({
    chest_pain_frequency: '',
    exercise_tolerance: '',
    family_heart_history: '',
    current_medications: '',
    smoking_status: ''
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success', 'error', 'processing'
  const [currentJobId, setCurrentJobId] = useState(null);

  // Load form data on component mount
  useEffect(() => {
    loadFormData();
  }, []);

  // Poll job status if there's an active job
  useEffect(() => {
    let interval;
    if (currentJobId && isProcessing) {
      interval = setInterval(async () => {
        try {
          const response = await jobAPI.getStatus(currentJobId);
          const { status, result, error } = response.data;
          
          if (status === 'completed') {
            setIsProcessing(false);
            setCurrentJobId(null);
            setMessage('Grammar improvement completed successfully!');
            setMessageType('success');
            
            // Reload the form data to show updated information
            loadFormData();
          } else if (status === 'failed') {
            setIsProcessing(false);
            setCurrentJobId(null);
            setMessage(`Grammar improvement failed: ${error || 'Unknown error'}`);
            setMessageType('error');
          }
        } catch (error) {
          console.error('Error checking job status:', error);
        }
      }, 2000); // Poll every 2 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [currentJobId, isProcessing]);

  const loadFormData = async () => {
    try {
      const response = await formAPI.get();
      const form = response.data;
      setFormData({
        chest_pain_frequency: form.chest_pain_frequency || '',
        exercise_tolerance: form.exercise_tolerance || '',
        family_heart_history: form.family_heart_history || '',
        current_medications: form.current_medications || '',
        smoking_status: form.smoking_status || ''
      });
      
      // Check if form is currently being processed
      if (form.is_processing && form.job_id) {
        setIsProcessing(true);
        setCurrentJobId(form.job_id);
        setMessage('Grammar improvement in progress...');
        setMessageType('processing');
      }
    } catch (error) {
      console.error('Error loading form data:', error);
      setMessage('Failed to load form data');
      setMessageType('error');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      await formAPI.update(formData);
      setMessage('Health data saved successfully!');
      setMessageType('success');
    } catch (error) {
      console.error('Error saving form data:', error);
      setMessage('Failed to save health data');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImproveGrammar = async () => {
    try {
      setIsProcessing(true);
      setMessage('Starting grammar improvement...');
      setMessageType('processing');
      
      const response = await formAPI.improveGrammar();
      setCurrentJobId(response.data.job_id);
      
      setMessage('Grammar improvement initiated. Please wait...');
    } catch (error) {
      console.error('Error triggering grammar improvement:', error);
      setIsProcessing(false);
      setMessage('Failed to start grammar improvement');
      setMessageType('error');
    }
  };

  return (
    <div className="container">
      <div className="header">
        <h1>Healthcare Form Platform</h1>
        <p>Cardiovascular Health Assessment</p>
      </div>

      {/* Status Messages */}
      {message && (
        <div className={`status-indicator status-${messageType}`}>
          {isProcessing && <div className="loading-spinner"></div>}
          {message}
        </div>
      )}

      {/* Main Form */}
      <div className="card">
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="chest_pain_frequency">
              1. How often do you experience chest pain or discomfort?
            </label>
            <textarea
              id="chest_pain_frequency"
              name="chest_pain_frequency"
              value={formData.chest_pain_frequency}
              onChange={handleInputChange}
              placeholder="Describe frequency and circumstances of chest pain..."
            />
          </div>

          <div className="form-group">
            <label htmlFor="exercise_tolerance">
              2. How well can you tolerate physical exercise or exertion?
            </label>
            <textarea
              id="exercise_tolerance"
              name="exercise_tolerance"
              value={formData.exercise_tolerance}
              onChange={handleInputChange}
              placeholder="Describe your exercise capacity, limitations, and symptoms during activity..."
            />
          </div>

          <div className="form-group">
            <label htmlFor="family_heart_history">
              3. Do you have a family history of heart disease?
            </label>
            <textarea
              id="family_heart_history"
              name="family_heart_history"
              value={formData.family_heart_history}
              onChange={handleInputChange}
              placeholder="Describe any family history of cardiovascular conditions..."
            />
          </div>

          <div className="form-group">
            <label htmlFor="current_medications">
              4. What medications are you currently taking?
            </label>
            <textarea
              id="current_medications"
              name="current_medications"
              value={formData.current_medications}
              onChange={handleInputChange}
              placeholder="List all current medications, including dosages if known..."
            />
          </div>

          <div className="form-group">
            <label htmlFor="smoking_status">
              5. What is your smoking status and history?
            </label>
            <textarea
              id="smoking_status"
              name="smoking_status"
              value={formData.smoking_status}
              onChange={handleInputChange}
              placeholder="Describe current smoking status, history, and cessation efforts..."
            />
          </div>

          <div style={{ display: 'flex', gap: '10px', marginTop: '30px' }}>
            <button 
              type="submit" 
              className="btn btn-secondary"
              disabled={isLoading || isProcessing}
            >
              {isLoading ? 'Saving...' : 'Save Health Data'}
            </button>
            
            <button 
              type="button" 
              onClick={handleImproveGrammar}
              className="btn btn-primary"
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Improve Grammar (save first)'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default HealthForm; 