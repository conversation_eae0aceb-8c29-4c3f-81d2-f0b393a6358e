# Frontend Setup Guide

## Prerequisites

- Node.js 16+
- npm or yarn

## Installation

1. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn install
   ```

## Running the Application

1. **Start the development server:**
   ```bash
   npm start
   # or
   yarn start
   ```

2. **Access the application:**
   - Frontend: http://localhost:3000
   - The app will automatically proxy API requests to http://localhost:8000

## Features

- **Health Assessment Form**: 5 cardiovascular health questions
  - Chest pain frequency
  - Exercise tolerance
  - Family heart disease history
  - Current medications
  - Smoking status
- **Real-time Processing**: Live feedback for LLM grammar improvement
- **Grammar Enhancement**: AI-powered grammar checking with an LLM
- **Auto-save**: Form data is automatically saved as you type
- **Job Status Tracking**: Visual feedback for background operations

## Technology Stack

- **React**: UI library with functional components and hooks
- **Axios**: HTTP client for API communication
- **CSS**: Modern styling with responsive design

## How It Works

### User Experience
1. User fills out the 5-question health form
2. Data is automatically saved to the backend
3. User can trigger "Improve Grammar" to enhance text quality
4. Background job processes the data using an LLM
5. Results are displayed with any grammar improvements made

### API Integration
- Form data is synchronized with backend JSON storage
- Real-time job status polling provides immediate feedback
- Error handling for network issues and API failures
- Graceful loading states throughout the application
